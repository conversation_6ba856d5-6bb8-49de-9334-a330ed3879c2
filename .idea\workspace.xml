<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="009376c7-5680-45bf-a660-985777ba922c" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/react_agent/configuration.py">
          <provider selected="true" editor-type-id="text-editor">
            <state>
              <folding>
                <element signature="e#57#91#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/react_agent/graph.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="285">
              <caret line="15" column="45" selection-start-line="15" selection-start-column="30" selection-end-line="15" selection-end-column="45" />
              <folding>
                <element signature="e#104#138#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-12" />
    <option name="y" value="145" />
    <option name="width" value="1932" />
    <option name="height" value="1045" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="langgraph-app" type="b2602c69:ProjectViewProjectNode" />
              <item name="langgraph-app" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="langgraph-app" type="b2602c69:ProjectViewProjectNode" />
              <item name="langgraph-app" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="langgraph-app" type="b2602c69:ProjectViewProjectNode" />
              <item name="langgraph-app" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="react_agent" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="009376c7-5680-45bf-a660-985777ba922c" name="Default Changelist" comment="" />
      <created>1747204968693</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747204968693</updated>
      <workItem from="1747204969948" duration="640000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="640000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Favorites" side_tool="true" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24946696" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info anchor="bottom" id="Python Console" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="SciView" />
      <window_info anchor="right" id="Database" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/react_agent/configuration.py">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <folding>
            <element signature="e#57#91#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/react_agent/utils.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="95">
          <caret line="5" lean-forward="true" selection-start-line="5" selection-end-line="5" />
          <folding>
            <element signature="e#35#84#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/react_agent/graph.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="15" column="45" selection-start-line="15" selection-start-column="30" selection-end-line="15" selection-end-column="45" />
          <folding>
            <element signature="e#104#138#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>