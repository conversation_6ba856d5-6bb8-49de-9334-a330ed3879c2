"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

from datetime import UTC, datetime
from typing import Dict, List, Literal, cast

from langchain_core.messages import AIMessage, ToolMessage
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from react_agent.configuration import Configuration
from react_agent.state import InputState, State
# from react_agent.tools import TOOLS
from react_agent.utils import load_chat_model
from dotenv import load_dotenv

# Define the function that calls the model
import json
from typing import Any, Callable, List, Optional, cast

from langchain_tavily import TavilySearch  # type: ignore[import-not-found]

from react_agent.configuration import Configuration


async def neo4j_search(query: str, tool_call_id: str) -> Optional[dict[str, Any]]:
    """
    当与Cypher查询语句相关，比如包含‘MATCH (c:Construct)-[: BELONGS_TO]->(:Segment)-[: BELONGS_TO]->(s:Section {name: "SG01"}) RETURN count(c)’这样的语句时，则调用该工具。
    query参数为Cypher查询语句，tool_call_id参数为additional_kwargs中tool_calls中的id。
    """
    print('query:')
    print(query)
    print('tool_call_id:')
    print(tool_call_id)
    db_result = []
    try:
        from neo4j import GraphDatabase
       
        uri = 'bolt://192.168.1.102:7687'
        user = 'neo4j'
        password = 'zwdneo4j'
        # 使用非异步方式连接Neo4j
        driver = GraphDatabase.driver(uri, auth=(user, password))

        # 使用with语句确保会话正确关闭
        with driver.session() as session:
            result = session.run(query)
            records = [record.data() for record in result]
            neo_result = json.dumps(records, ensure_ascii=False)
            print('neo_result')
            print(neo_result)
            db_result = neo_result

        # 关闭驱动
        driver.close()
    except Exception as e:
        print(f"执行Neo4j查询时出错: {e}")
    # configuration = Configuration.from_context()
    # wrapped = TavilySearch(max_results=configuration.max_search_results)
    return {"messages": [ToolMessage(content=db_result, tool_call_id=tool_call_id)]}

async def chart(chart_type, chart_data, tool_call_id: str) -> Optional[dict[str, Any]]:
    """
    chart_data参数为图表数据，如果是柱状图，则type为bar，如果是饼图，则type为pie，将涉及到统计的总结回答直接转换为图表数据，chart_data遵循固定格式:
    {
      "labels": ["封堵墙", "暗挖初支", "暗挖二衬", "明挖主体", "斜井"],
      "data": [30, 25, 20, 15, 10],
      "title": "构件分布"
    }
    """
    print('chart_type:')
    print(chart_type)
    print('chart_data:')
    print(chart_data)

    # 将 labels 和 data 组合成元组列表，并按 data 升序排序
    combined = list(zip(chart_data["labels"], chart_data["data"]))
    sorted_combined = sorted(combined, key=lambda x: x[1])  # 默认升序（reverse=False）

    # 解压排序后的元组列表，得到排序后的 labels 和 data
    sorted_labels, sorted_data = zip(*sorted_combined)

    # 更新 chart_data
    chart_data["labels"] = list(sorted_labels)
    chart_data["data"] = list(sorted_data)

    print(chart_data)
    return json.dumps({
        "chart_type": chart_type,
        "chart_data": chart_data
    }, ensure_ascii=False)
    # return {"messages": [ToolMessage(content={chart_data}, tool_call_id=tool_call_id)]}


TOOLS: List[Callable[..., Any]] = [neo4j_search]
TOOLS2: List[Callable[..., Any]] = [chart]

async def first_call_mode(state: State) -> Dict[str, List[AIMessage]]:
    """Call the LLM powering our "agent".

    This function prepares the prompt, initializes the model, and processes the response.

    Args:
        state (State): The current state of the conversation.
        config (RunnableConfig): Configuration for the model run.

    Returns:
        dict: A dictionary containing the model's response message.
    """
    print('state:')
    print(state)
    # configuration = Configuration.from_context()
    # print('configuration:')
    # print(configuration)

    # # Initialize the model with tool binding. Change the model or add more tools here.
    # model = load_chat_model(configuration.model).bind_tools(TOOLS)
    # print('model:')
    # print(model)
    
    
    from langchain_openai import ChatOpenAI



    model = ChatOpenAI(
        model="deepseek-ai/DeepSeek-V3",
        temperature=0,
        max_retries=2,
        api_key="sk-mqdiudcdydopxjrnwqgwbkauzgsljdireyvpipevdvghhhed",
        base_url="https://api.siliconflow.cn"
    ).bind_tools(TOOLS)
    # model = ChatOpenAI(
    #     model="kimi-k2-0711-preview",
    #     temperature=0,
    #     max_retries=2,
    #     api_key="sk-1Ifw4iPHsRmgK3Z6jKZVX2fwvNlzHiLUURS0HqJp6hzC8NQe",
    #     base_url="https://api.moonshot.cn/v1"
    # ).bind_tools(TOOLS)
    print('model:')
    print(model)


    # Format the system prompt. Customize this to change the agent's behavior.
    # system_message = configuration.system_prompt.format(
    #     system_time=datetime.now(tz=UTC).isoformat()
    # )

    system_message = """
     作为专门为生成Neo4j Cypher查询而设计的专用工具，您的功能是将自然语言查询直接转换为精确且可执行的Cypher请求。

您将利用提供的数据库模式和可选的几个快照示例来理解Neo4j数据库中的结构、关系和以前的查询模式，从而相应地制定您的响应。
    说明：
    严格的响应格式：您的响应必须仅以可执行的Cypher查询的形式。任何不属于Cypher查询语法的解释、上下文或附加信息都应该完全省略。
    模式：模式描述了数据库的结构，包括节点标签及其属性，并包含在<Schema>标签中。
    在收到用户问题后，综合模式和任何示例，以制作一个与用户意图直接对应的精确Cypher查询。
    处理一般查询：对于要求提供Cypher查询直接生成之外的信息或功能的查询，请使用Cypher询问格式来传达限制或功能。 
    例如：RETURN"我的设计目的是仅根据提供的模式生成Cypher查询。"
    联合查询的一致性：在生成涉及Union的查询时，确保Union的所有部分具有相同的列名以保持一致性，并且各个部分都有自己的返回语句。
    请结合<FEW_SHOT_EXAMPLES>标签中的参考示例回答。
    在回答一般问题时，一定要提到问题超出了给定的模式范围。虽然我的回答是为了提供信息和准确，但它们不是基于数据库查询，因此不应被视为权威的信息来源。
    示例：对于如何连接到Neo4j数据库的查询，您的响应仍应遵循Cypher查询格式：RETURN"要连接到Neo4j数据库，请使用适当的Neo4j驱动程序，并按照官方文档了解配置详细信息。"
    目标：您的主要目标是将用户查询转换为可以在Neo4j数据库中立即执行的直接Cypher查询。即使在一般或范围外的询问中，也不要生成不符合此格式的回复。
    <Schema>
         注意：请使用中文回答
    数据库里有7个节点标签，分别是 构件Construct(只有name属性，代表构件名称)、节段Segment(只有name属性，代表节段名称)、 标段Section(只有name属性，代表标段名称)、图层Layer(只有name属性，代表节图层名称)、日Day(只有name属性，代表某一天，格式如2021-01-01)、月Month(只有name属性，代表某一月，格式如2021-01)、年Year(只有name属性，代表某一年，格式如2021)。有以下几种关系，分别是 某构件隶属于（BELONGS_TO）某节段、某节段隶属于（BELONGS_TO）某标段、某构件隶属于（BELONGS_TO）某图层、某日隶属于（BELONGS_TO）某月、某月隶属于（BELONGS_TO）某年、某构件施工计划开始于（PLANSTART_AT）某日、某构件施工计划结束于（PLANEND_AT）某日、某构件施工实际开始于（ACTUALSTART_AT）某日、某构件施工实际结束于ACTUALEND_AT某日
    PLANSTART_AT代表某构件的计划施工开始的日期，PLANEND_AT代表某构件的计划施工结束的日期，ACTUALSTART_AT代表某构件的实际施工开始的日期，ACTUALEND_AT代表某构件的实际施工结束的日期。当被问及构件进度相关的问题时，一定要返回对应的四个时间，对应时间没有则使用OPTIONAL关键字。
    </Schema>
    <FEW_SHOT_EXAMPLES>
        Ask:
    有多少个标段？
    Cypher Query:
    MATCH (n:Section) RETURN n
    </FEW_SHOT_EXAMPLES>
    注意：
    生成的Cypher语句在比较时间时，请转化为date格式再比较，例如：MATCH (c:Construct)-[:ACTUALEND_AT]->(d:Day) WHERE date(toString(d.name)) <= date() RETURN count(c)。
    当有条件查询时，WHERE子句一定不要使用模式表达式（Pattern Expression）。
    当被问及所有构件或某一标段的所有构件进度概况这类问题时,在生成cypher查询语句必须带上进度状态计算,查询结果返回各状态的数量统计。
        构件进度状态计算逻辑如下：
        当构件没有实际施工开始的日期，则状态包含未开始（或未启动、未施工）；
        当构件有实际施工开始的日期，无实际施工结束的日期，则状态包含进行中（或运行中、施工中）；
        当构件有实际施工结束的日期，则状态包含已完成（已结束、一施工）；
        当构件缺少计划施工开始日期和计划施工结束日期，则状态包含未逾期（或按计划完成）；
        当构件有计划施工结束日期，有实际施工结束日期，并且计划施工结束日期大于或等于实际施工结束日期，则状态包含未逾期（或按计划完成）；
        当构件有计划施工结束日期，有实际施工结束日期，并且计划施工结束日期小于实际施工结束日期，则状态包含逾期（或未按计划完成）；
    最后：
    根据上述所有信息和说明，结合用户问题HumanMessage生成cypher查询语句。
    精准的查询语句生成之后，请一定要调用neo4j_search函数，查询数据库，返回查询结果。

  
    """

    # Get the model's response
    response = cast(
        AIMessage,
        await model.ainvoke(
            [{"role": "system", "content": system_message}, *state.messages]
        ),
    )
    print('response:')
    # response.tool_calls = [{'name': 'search', 'args': ' {"query": "MATCH (c:Construct)-[:BELONGS_TO]->(:Segment)-[:BELONGS_TO]->(s:Section {name: "SG01"}) RETURN count(c)"}', 'id': '0196d31b1a98f3dee7189f19a9692e0e', 'error': None, 'type': 'tool_call'}]
    print(response)

    # Handle the case when it's the last step and the model still wants to use a tool
    # if state.is_last_step and response.tool_calls:
    #     return {
    #         "messages": [
    #             AIMessage(
    #                 id=response.id,
    #                 content="Sorry, I could not find an answer to your question in the specified number of steps.",
    #             )
    #         ]
    #     }

    # Return the model's response as a list to be added to existing messages
    return {"messages": [response]}


async def second_call_mode(state: State) -> Dict[str, List[AIMessage]]:
    """Call the LLM powering our "agent".

    This function prepares the prompt, initializes the model, and processes the response.

    Args:
        state (State): The current state of the conversation.
        config (RunnableConfig): Configuration for the model run.

    Returns:
        dict: A dictionary containing the model's response message.
    """
    print('state:')
    print(state)
    # configuration = Configuration.from_context()
    # print('configuration:')
    # print(configuration)

    # # Initialize the model with tool binding. Change the model or add more tools here.
    # model = load_chat_model(configuration.model).bind_tools(TOOLS)
    # print('model:')
    # print(model)
    
    
    from langchain_openai import ChatOpenAI

    model = ChatOpenAI(
        model="deepseek-ai/DeepSeek-V3",
        temperature=0,
        max_retries=2,
        api_key="sk-mqdiudcdydopxjrnwqgwbkauzgsljdireyvpipevdvghhhed",
        base_url="https://api.siliconflow.cn"
    ).bind_tools(TOOLS2)
    print('model2:')
    print(model)


    # Format the system prompt. Customize this to change the agent's behavior.
    # system_message = configuration.system_prompt.format(
    #     system_time=datetime.now(tz=UTC).isoformat()
    # )

    system_message = """
     任务概述： 你的任务有2点，1是将主要以 JSON 格式存在的结构化查询响应数据，转换成人类易于阅读的格式，并且请一定要转化为Markdown格式再进行回答。2是请一定将 JSON 格式存在的结构化查询响应数据转化为图表的固定格式数据，并以柱状图或者饼图展示。
     这不仅包括格式化列表和数组，还包括在必要时解释或总结内容。目标是通过清晰、简洁的方式呈现数据，从而提高数据的可访问性。
操作说明：
理解上下文： 你会收到一个问题及其对应的 JSON 响应。你的工作是解释这些数据，并将其重新格式化为易于阅读的格式。
格式化指南： 遵循提供的示例作为转换的基本指南。但是，请你一定使用 Markdown 格式来提高可读性和清晰度。这可能包括使用项目符号列表、编号列表或粗体来强调。
处理不同数据类型： 虽然 JSON 是主要预期的格式，但请准备好遇到其他格式的响应。在这种情况下，重点在于以更易于理解的方式阐明数据，而不必严格遵守 JSON 格式化规则。
示例转换：
问题： 什么是不同的监视术语？
JSON 响应：
["alert", "attorney", "bad", "canceled", "charge"]
人类易读输出：
以下是不同的监视术语：
- alert
- attorney
- bad
- canceled
- charge
你的目标是： 给定一个问题 (%s) 及其对应的回答 (%s)，生成一个人类易读的摘要或列表，能够有效地将信息传达给非专业读者。审慎地运用格式化来提升数据的呈现和理解。
补充说明： 灵活处理数据和创造性地使用格式化是关键。始终以输出的清晰性和可访问性为目标。让语气听起来像自然的专业对话，避免夸大事实，并避免再次解释问题，以及说"这是人类易读的格式"等等的话。
 
当被问及构件的进度状态时，你需要找到构件的四个时间，再结合参照时间进行对比计算出构件的进度状态，通常参照时间是当前时间，如果问题中明确指出哪个时间，则使用这个时间为参照时间。
构件状态计算逻辑如下：
    当构件没有实际施工开始的日期，则状态包含未开始（或未启动、未施工）；
    当构件有实际施工开始的日期，无实际施工结束的日期，则状态包含进行中（或运行中、施工中）；
    当构件有实际施工结束的日期，则状态包含已完成（已结束、一施工）；
    当构件缺少计划施工开始日期和计划施工结束日期，则状态包含未逾期（或按计划完成）；
    当构件有计划施工结束日期，有实际施工结束日期，并且计划施工结束日期大于或等于实际施工结束日期，则状态包含未逾期（或按计划完成）；
    当构件有计划施工结束日期，有实际施工结束日期，并且计划施工结束日期小于实际施工结束日期，则状态包含逾期（或未按计划完成）；
当被问及所有构件或某一标段的所有构件进度概况这类问题时,请按照查询结果返回各状态的数量统计回答总体情况。
当被问及关于统计的问题时，请按照查询结果回答总体情况，并根据总体情况，调用chart工具，将统计数据转化为图表的固定格式，目前仅支持柱状图或者饼图，请将统计数据转化为柱状图或者饼图的固定格式。
无论如何请一定要回答内容，不要为空，最终回答，请以Markdown格式返回，并且对内容进行强调，比如加粗，斜体，标题，列表，图片，链接，代码块等等。请勿返回纯文本或其他格式。
    """

    # Get the model's response
    response = cast(
        AIMessage,
        await model.ainvoke(
            [{"role": "system", "content": system_message}, *state.messages]
        ),
    )
    print('response:')
    # response.tool_calls = [{'name': 'search', 'args': ' {"query": "MATCH (c:Construct)-[:BELONGS_TO]->(:Segment)-[:BELONGS_TO]->(s:Section {name: "SG01"}) RETURN count(c)"}', 'id': '0196d31b1a98f3dee7189f19a9692e0e', 'error': None, 'type': 'tool_call'}]
    print(response)

    # Handle the case when it's the last step and the model still wants to use a tool
    # if state.is_last_step and response.tool_calls:
    #     return {
    #         "messages": [
    #             AIMessage(
    #                 id=response.id,
    #                 content="Sorry, I could not find an answer to your question in the specified number of steps.",
    #             )
    #         ]
    #     }

    # Return the model's response as a list to be added to existing messages
    return {"messages": [response]}


# Define a new graph

builder = StateGraph(State, input=InputState, config_schema=Configuration)

# Define the two nodes we will cycle between
builder.add_node(first_call_mode)
builder.add_node(second_call_mode)
builder.add_node("tools", ToolNode(TOOLS))
builder.add_node("tools2", ToolNode(TOOLS2))

# Set the entrypoint as `first_call_mode`
# This means that this node is the first one called
builder.add_edge("__start__", "first_call_mode")


def route_model_output(state: State) -> Literal["__end__", "tools"]:
    """Determine the next node based on the model's output.

    This function checks if the model's last message contains tool calls.

    Args:
        state (State): The current state of the conversation.

    Returns:
        str: The name of the next node to call ("__end__" or "tools").
    """
    last_message = state.messages[-1]
    if not isinstance(last_message, AIMessage):
        raise ValueError(
            f"Expected AIMessage in output edges, but got {type(last_message).__name__}"
        )
    # If there is no tool call, then we finish
    if not last_message.tool_calls:
        return "__end__"
    # Otherwise we execute the requested actions
    return "tools"


# Add a conditional edge to determine the next step after `first_call_mode`
builder.add_conditional_edges(
    "first_call_mode",
    # After first_call_mode finishes running, the next node(s) are scheduled
    # based on the output from route_model_output
    route_model_output,
)

# Add a normal edge from `tools` to `first_call_mode`
# This creates a cycle: after using tools, we always return to the model
builder.add_edge("tools", "second_call_mode")
builder.add_edge("second_call_mode", "tools2")

# Compile the builder into an executable graph
graph = builder.compile(name="ReAct Agent")
