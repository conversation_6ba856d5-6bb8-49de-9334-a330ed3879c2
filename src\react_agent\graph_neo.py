"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

from datetime import UTC, datetime
from typing import Dict, List, Literal, cast

from langchain_core.messages import AIMessage
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from react_agent.configuration import Configuration
from react_agent.state import InputState, State
from react_agent.tools import TOOLS
from react_agent.utils import load_chat_model
from dotenv import load_dotenv





from typing import Any, Callable, List, Optional, cast

from langchain_tavily import TavilySearch  # type: ignore[import-not-found]

from react_agent.configuration import Configuration


async def cypher_neo4j(query: str) -> Optional[dict[str, Any]]:
    """
    当与Cypher查询语句相关，则调用该工具，比如‘MATCH (c:Construct)-[: BELONGS_TO]->(:Segment)-[: BELONGS_TO]->(s:Section {name: "SG01"}) RETURN count(c)’
    """
    print('query')
    print(query)
    uri = 'bolt://192.168.1.102:7687'
    user = 'neo4j'
    password = 'zwdneo4j'

    # 打印调试信息
    cypher_query = query
    # cypher_query = cypher_query.replace('\n', ' ').replace('```', '')
    db_result = []
    try:
        from neo4j import GraphDatabase
        # 使用非异步方式连接Neo4j
        driver = GraphDatabase.driver(uri, auth=(user, password))

        # 使用with语句确保会话正确关闭
        with driver.session() as session:
            result = session.run(cypher_query)
            records = [record.data() for record in result]
            json_result = json.dumps(records, ensure_ascii=False)
            print(json_result)
            db_result = json_result

        # 关闭驱动
        driver.close()
    except Exception as e:
        print(f"执行Neo4j查询时出错: {e}")
    configuration = Configuration.from_context()
    wrapped = TavilySearch(max_results=configuration.max_search_results)
    return cast(dict[str, Any], await wrapped.ainvoke({"query": query}))


TOOLS: List[Callable[..., Any]] = [cypher_neo4j]


# Define the function that calls the model


async def second_call_model(state: State) -> Dict[str, List[AIMessage]]:
    """Call the LLM powering our "agent".

    This function prepares the prompt, initializes the model, and processes the response.

    Args:
        state (State): The current state of the conversation.
        config (RunnableConfig): Configuration for the model run.

    Returns:
        dict: A dictionary containing the model's response message.
    """
    print('state:')
    print(state)
    configuration = Configuration.from_context()
    # print('configuration:')
    # print(configuration)
    # Initialize the model with tool binding. Change the model or add more tools here.
    model = load_chat_model(configuration.model).bind_tools(TOOLS)
    # print('model:')
    # print(model)

    # from openai import OpenAI

    # client = OpenAI(
    #     api_key='AIzaSyBrnQr_3AZjJlYsRUyweTRfUaZc_0UaHgc',
    #     base_url='https://generativelanguage.googleapis.com/v1beta',
    # )
    # print('client:')
    # print(client)
    # import os
    # os.environ['BG_JOB_ISOLATED_LOOPS']= 'true'

    # # 创建聊天完成请求
    # response = client.chat.completions.create(
    #     model='gemini-2.0-flash',
    #     messages=[{"role": "user", "content": 'What is LangChain?'}],
    #     temperature=0.1,  # 低温度保证输出更加确定性
    #     max_tokens=2048,  # 确保足够长的输出
    # )
    # print('response:')
    # print(response)
    
    
    from langchain_openai import ChatOpenAI

    llm = ChatOpenAI(
        model="Qwen/Qwen3-32B",
        temperature=0,
        max_retries=2,
        api_key="sk-szpibkoyxvehwvrekpukvwrxcztcdwbngckemtezuslpxwrk",
        base_url="https://api.siliconflow.cn"
    ).bind_tools(TOOLS)
    # print('llm:')
    # print(llm)
    # response = llm.invoke("What is LangChain?")
    # print('response:')
    # print(response)
    model =llm


    # Format the system prompt. Customize this to change the agent's behavior.
    system_message = configuration.system_prompt.format(
        system_time=datetime.now(tz=UTC).isoformat()
    )

    # from react_agent import prompts
    
    # 数据库模式
    SCHEMA = """
    注意：请使用中文回答
    数据库里有7个节点标签，分别是 构件Construct、节段Segment、 标段Section、图层Layer、日Day、月Month、年Year。有以下几种关系，分别是 某构件隶属于（BELONGS_TO）某节段、某节段隶属于（BELONGS_TO）某标段、某构件隶属于（BELONGS_TO）某图层、某日隶属于（BELONGS_TO）某月、某月隶属于（BELONGS_TO）某年、某构件施工计划开始于（PLANSTART_AT）某日、某构件施工计划结束于（PLANEND_AT）某日、某构件施工实际开始于（ACTUALSTART_AT）某日、某构件施工实际结束于ACTUALEND_AT某日
    """

    # 示例查询
    FEW_SHOT_EXAMPLES = """
    Ask:
    有多少个标段？
    Cypher Query:
    MATCH (n:Section) RETURN n
    """

    # Cypher 生成提示词模板
    CYPHER_MAKER_TEMPLATE = """
    作为专门为生成Neo4j Cypher查询而设计的专用工具，您的功能是将自然语言查询直接转换为精确且可执行的Cypher请求。您将利用提供的数据库模式和可选的几个快照示例来理解Neo4j数据库中的结构、关系和以前的查询模式，从而相应地制定您的响应。
    说明：
    严格的响应格式：您的响应必须仅以可执行的Cypher查询的形式。任何不属于Cypher查询语法的解释、上下文或附加信息都应该完全省略。
    模式：模式描述了数据库的结构，包括节点标签及其属性，并包含在<Schema>标签中。
    在收到用户问题后，综合模式和任何示例，以制作一个与用户意图直接对应的精确Cypher查询。
    处理一般查询：对于要求提供Cypher查询直接生成之外的信息或功能的查询，请使用Cypher询问格式来传达限制或功能。 
    例如：RETURN"我的设计目的是仅根据提供的模式生成Cypher查询。"
    联合查询的一致性：在生成涉及Union的查询时，确保Union的所有部分具有相同的列名以保持一致性，并且各个部分都有自己的返回语句。
    继续和上下文处理：如果查询是继续或与前面的问题相关，请分析<HistoryOfConversation>标签中包含的上下文，以保持回答的一致性。
    在回答一般问题时，一定要提到问题超出了给定的模式范围。虽然我的回答是为了提供信息和准确，但它们不是基于数据库查询，因此不应被视为权威的信息来源。
    示例：对于如何连接到Neo4j数据库的查询，您的响应仍应遵循Cypher查询格式：RETURN"要连接到Neo4j数据库，请使用适当的Neo4j驱动程序，并按照官方文档了解配置详细信息。"
    目标：您的主要目标是将用户查询转换为可以在Neo4j数据库中立即执行的直接Cypher查询。即使在一般或范围外的询问中，也不要生成不符合此格式的回复。
    <Schema>
        %s
    </Schema>
    <HistoryOfConversation>
        %s
    </HistoryOfConversation>

    根据上述所有信息和说明，结合用户问题HumanMessage生成cypher查询语句,UserQuestion标签内容为用户问题。
    """
    system_message = CYPHER_MAKER_TEMPLATE % (
        SCHEMA,
        FEW_SHOT_EXAMPLES
    )
    print('system_message:')
    print(system_message)

    # Get the model's response
    response = cast(
        AIMessage,
        await model.ainvoke(
            [{"role": "system", "content": system_message}, *state.messages]
        ),
    )
    
    print('response:')
    print(response)

    # Handle the case when it's the last step and the model still wants to use a tool
    if state.is_last_step and response.tool_calls:
        return {
            "messages": [
                AIMessage(
                    id=response.id,
                    content="Sorry, I could not find an answer to your question in the specified number of steps.",
                )
            ]
        }

    # Return the model's response as a list to be added to existing messages
    return {"messages": [response]}

async def first_call_model(state: State) -> Dict[str, List[AIMessage]]:
    """Call the LLM powering our "agent".

    This function prepares the prompt, initializes the model, and processes the response.

    Args:
        state (State): The current state of the conversation.
        config (RunnableConfig): Configuration for the model run.

    Returns:
        dict: A dictionary containing the model's response message.
    """
    print('state:')
    print(state)
    configuration = Configuration.from_context()
    # print('configuration:')
    # print(configuration)
    # Initialize the model with tool binding. Change the model or add more tools here.
    model = load_chat_model(configuration.model).bind_tools(TOOLS)
    # print('model:')
    # print(model)

    # from openai import OpenAI

    # client = OpenAI(
    #     api_key='AIzaSyBrnQr_3AZjJlYsRUyweTRfUaZc_0UaHgc',
    #     base_url='https://generativelanguage.googleapis.com/v1beta',
    # )
    # print('client:')
    # print(client)
    # import os
    # os.environ['BG_JOB_ISOLATED_LOOPS']= 'true'

    # # 创建聊天完成请求
    # response = client.chat.completions.create(
    #     model='gemini-2.0-flash',
    #     messages=[{"role": "user", "content": 'What is LangChain?'}],
    #     temperature=0.1,  # 低温度保证输出更加确定性
    #     max_tokens=2048,  # 确保足够长的输出
    # )
    # print('response:')
    # print(response)
    
    
    from langchain_openai import ChatOpenAI

    llm = ChatOpenAI(
        model="Qwen/Qwen3-32B",
        temperature=0,
        max_retries=2,
        api_key="sk-szpibkoyxvehwvrekpukvwrxcztcdwbngckemtezuslpxwrk",
        base_url="https://api.siliconflow.cn"
    ).bind_tools(TOOLS)
    # print('llm:')
    # print(llm)
    # response = llm.invoke("What is LangChain?")
    # print('response:')
    # print(response)
    model =llm


    # Format the system prompt. Customize this to change the agent's behavior.
    system_message = configuration.system_prompt

  
    print('system_message:')
    print(system_message)

    # Get the model's response
    response = cast(
        AIMessage,
        await model.ainvoke(
            [{"role": "system", "content": system_message}, *state.messages]
        ),
    )
    
    print('response:')
    print(response)

    # Handle the case when it's the last step and the model still wants to use a tool
    # if state.is_last_step and response.tool_calls:
    #     return {
    #         "messages": [
    #             AIMessage(
    #                 id=response.id,
    #                 content="Sorry, I could not find an answer to your question in the specified number of steps.",
    #             )
    #         ]
    #     }

    # Return the model's response as a list to be added to existing messages
    return {"messages": [response]}


# Define a new graph

builder = StateGraph(State, input=InputState, config_schema=Configuration)

# Define the two nodes we will cycle between
builder.add_node(first_call_model)
builder.add_node(second_call_model)
builder.add_node("tools", ToolNode(TOOLS))

# Set the entrypoint as `first_call_model`
# This means that this node is the first one called
builder.add_edge("__start__", "first_call_model")


def route_model_output(state: State) -> Literal["__end__", "tools"]:
    """Determine the next node based on the model's output.

    This function checks if the model's last message contains tool calls.

    Args:
        state (State): The current state of the conversation.

    Returns:
        str: The name of the next node to call ("__end__" or "tools").
    """
    # last_message = state.messages[-1]
    # if not isinstance(last_message, AIMessage):
    #     raise ValueError(
    #         f"Expected AIMessage in output edges, but got {type(last_message).__name__}"
    #     )
    # # If there is no tool call, then we finish
    # if not last_message.tool_calls:
    #     return "__end__"
    # Otherwise we execute the requested actions
    return "tools"


# Add a conditional edge to determine the next step after `first_call_model`
# builder.add_conditional_edges(
#     "first_call_model",
#     # After first_call_model finishes running, the next node(s) are scheduled
#     # based on the output from route_model_output
#     route_model_output,
# )

# Add a normal edge from `tools` to `first_call_model`
# This creates a cycle: after using tools, we always return to the model
builder.add_edge("first_call_model", "tools")
builder.add_edge("tools", "second_call_model")
# builder.add_edge("tools", "first_call_model")

# Compile the builder into an executable graph
graph = builder.compile(name="ReAct Agent")
