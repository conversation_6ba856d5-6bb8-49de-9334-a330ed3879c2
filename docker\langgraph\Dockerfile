FROM ubuntu:16.04

WORKDIR /langgraph-dir
RUN pwd


# 安装编译依赖
RUN apt update
RUN apt install -y build-essential zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libssl-dev libreadline-dev libffi-dev curl libbz2-dev wget git vim

RUN git clone http://192.168.1.98:9980/project-group/langgraph-app.git

ENV SSL_DIR=/usr/local/ssl
# 下载 Python 3.13 源码
# RUN wget https://www.python.org/ftp/python/3.13.0/Python-3.13.0.tgz

# 安装 Python 3.13
WORKDIR /langgraph-dir/langgraph-app/soft
RUN tar -xzf Python-3.13.0.tgz
WORKDIR /langgraph-dir/langgraph-app/soft/Python-3.13.0
RUN ./configure --enable-optimizations
RUN make -j$(nproc)
RUN make altinstall

# 安装 openssl
WORKDIR /langgraph-dir/langgraph-app/soft
RUN tar -zxvf openssl-1.1.1l.tar.gz
WORKDIR /langgraph-dir/langgraph-app/soft/openssl-1.1.1l
RUN ./config --prefix=${SSL_DIR} --openssldir=${SSL_DIR}
RUN make && make install

# 重新编译python3.13
WORKDIR /langgraph-dir/langgraph-app/soft/Python-3.13.0
RUN ./configure --with-zlib=/usr/include/ --with-openssl-rpath=auto  --with-openssl=${SSL_DIR}  OPENSSL_LDFLAGS=-L${SSL_DIR}   OPENSSL_LIBS=-l${SSL_DIR} OPENSSL_INCLUDES=-I${SSL_DIR}
RUN make -j$(nproc)
RUN make altinstall

# 安装python依赖
WORKDIR /langgraph-dir/langgraph-app
RUN pip3.13 install -U "langgraph-cli[inmem]"
RUN pip3.13 install -e .
RUN pip3.13 install neo4j


EXPOSE 8092
CMD ["langgraph", "dev", "--host", "0.0.0.0", "--port", "8092", "--allow-blocking"]

